# 文件: models/mamba_block.py
# 简化的MambaBlock实现，用于CIFT时序处理

import torch
import torch.nn as nn
import torch.nn.functional as F

class MambaBlock(nn.Module):
    """
    简化的Mamba块，用于时序特征处理
    输入: (B, T, D) - 批次、时间步、特征维度
    输出: (B, T, D) - 相同维度的增强特征
    """
    def __init__(self, d_model=256, d_state=16, expand=2):
        super().__init__()
        self.d_model = d_model
        self.d_state = d_state
        self.d_inner = int(expand * d_model)
        
        # 输入投影
        self.in_proj = nn.Linear(d_model, self.d_inner * 2, bias=False)
        
        # 状态空间参数
        self.A_log = nn.Parameter(torch.randn(self.d_inner, d_state))
        self.D = nn.Parameter(torch.ones(self.d_inner))
        
        # 输出投影
        self.out_proj = nn.Linear(self.d_inner, d_model, bias=False)
        
        # 层归一化
        self.norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        """
        前向传播
        x: (B, T, D)
        """
        B, T, D = x.shape
        
        # 残差连接
        residual = x
        
        # 层归一化
        x = self.norm(x)
        
        # 输入投影
        x_and_res = self.in_proj(x)  # (B, T, 2*d_inner)
        x, res = x_and_res.split([self.d_inner, self.d_inner], dim=-1)
        
        # 简化的状态空间处理 (这里用GRU近似)
        # 在实际实现中，这里会是复杂的状态空间计算
        x = x.transpose(0, 1)  # (T, B, d_inner)
        
        # 使用简单的线性变换模拟状态空间
        A = -torch.exp(self.A_log.float())  # (d_inner, d_state)
        
        # 简化处理：使用平均池化模拟状态传播
        x_processed = []
        for t in range(T):
            if t == 0:
                h = torch.zeros(B, self.d_state, device=x.device, dtype=x.dtype)
            
            # 简化的状态更新
            h = h * 0.9 + x[t] @ A * 0.1  # 简化的状态传播
            x_t = x[t] + h @ A.T * self.D  # 简化的输出计算
            x_processed.append(x_t)
        
        x = torch.stack(x_processed, dim=0)  # (T, B, d_inner)
        x = x.transpose(0, 1)  # (B, T, d_inner)
        
        # 门控
        x = x * torch.sigmoid(res)
        
        # 输出投影
        x = self.out_proj(x)
        
        # 残差连接
        return x + residual
