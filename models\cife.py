# 文件: models/cife.py

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from einops import rearrange, repeat

class MambaBlock(nn.Module):
    """
    一个简化的 Mamba 块实现，改编自 samba.py 以用于图像块序列处理。
    A simplified Mamba block implementation, adapted from samba.py for image patch sequence processing.
    Reference: samba.py
    """
    def __init__(
        self,
        d_model: int,
        d_state: int = 16,
        expand: int = 2,
        d_conv: int = 4,
        conv_bias: bool = True,
        bias: bool = False,
    ):
        super().__init__()
        self.d_model = d_model
        self.d_state = d_state
        self.expand = expand
        self.d_conv = d_conv
        self.conv_bias = conv_bias
        
        d_inner = int(self.expand * self.d_model)
        self.d_inner = d_inner

        # Mamba的核心组件
        self.in_proj = nn.Linear(d_model, d_inner * 2, bias=bias)
        self.conv1d = nn.Conv1d(
            in_channels=d_inner,
            out_channels=d_inner,
            bias=conv_bias,
            kernel_size=d_conv,
            groups=d_inner,
            padding=d_conv - 1,
        )
        self.x_proj = nn.Linear(d_inner, d_state * 2, bias=False) # 预测B和C
        self.dt_proj = nn.Linear(d_inner, d_inner, bias=True)

        A = repeat(torch.arange(1, d_state + 1), 'n -> d n', d=d_inner)
        self.A_log = nn.Parameter(torch.log(A))
        self.D = nn.Parameter(torch.ones(d_inner))
        self.out_proj = nn.Linear(d_inner, d_model, bias=bias)
        
        self.norm = nn.LayerNorm(d_model)

    def forward(self, x):
        """
        x: (B, L, D) - B是批次大小（如此处的proposal数量），L是序列长度（patch数量），D是特征维度
        """
        # 残差连接
        residual = x
        x = self.norm(x)

        # 投影和分割
        x_and_res = self.in_proj(x)
        x, res = x_and_res.split(split_size=[self.d_inner, self.d_inner], dim=-1)

        # 1D卷积和激活
        x = rearrange(x, 'b l d -> b d l')
        x = self.conv1d(x)[:, :, :x.shape[-1]] # 因果卷积
        x = rearrange(x, 'b d l -> b l d')
        x = F.silu(x)

        # SSM计算
        y = self.ssm(x)
        
        # 门控和输出
        y = y * F.silu(res)
        output = self.out_proj(y) + residual
        
        return output

    def ssm(self, x):
        (d_in, n) = self.A_log.shape
        A = -torch.exp(self.A_log.float())
        D = self.D.float()
        
        # 简化选择性机制，让B和C成为可学习参数
        BC = self.x_proj(x) # (B, L, d_inner) -> (B, L, d_state * 2)
        B, C = BC.chunk(2, dim=-1) # 分割为B和C，每个都是 (B, L, d_state)
        delta = F.softplus(self.dt_proj(x))

        # 离散化 (ZOH)
        deltaA = torch.exp(delta.unsqueeze(-1) * A)
        deltaB = delta.unsqueeze(-1) * B.unsqueeze(2)  # (B, L, 1, d_state)

        # 扫描
        h = torch.zeros(x.size(0), self.d_inner, self.d_state, device=x.device)
        ys = []
        for i in range(x.size(1)):
            h = deltaA[:, i] * h + deltaB[:, i] * x[:, i].unsqueeze(-1)
            y = torch.sum(h * C[:, i].unsqueeze(1), dim=-1)  # (B, d_inner)
            ys.append(y)
        y = torch.stack(ys, dim=1)
        
        y = y + x * D
        return y


class CIFE(nn.Module):
    """
    Causal Interaction Feature Extractor.
    从图像块中提取特征。
    """
    def __init__(self, d_model=256, n_layers=4, patch_size=16, state_dim=16):
        super().__init__()
        self.d_model = d_model
        self.patch_size = patch_size
        
        # 1. PatchTokenizer: 将 (B, C, H, W) 的图像块转换为 (B, L, D) 的序列
        self.tokenizer = nn.Conv2d(3, d_model, kernel_size=patch_size, stride=patch_size)
        self.pos_embed = nn.Parameter(torch.zeros(1, 128*128, d_model)) # 假设最大patch数量
        
        # 2. MambaImageEncoder: Mamba块堆栈
        self.encoder = nn.ModuleList([
            MambaBlock(d_model=d_model, d_state=state_dim) for _ in range(n_layers)
        ])
        
        # 3. TokenFusion: 从序列中获得单一特征向量
        self.fusion = nn.AdaptiveAvgPool1d(1)
        self.norm = nn.LayerNorm(d_model)

        # ================== 第2步：添加置信度预测头 ==================
        # 定义一个MLP，用于从提纯后的特征中预测置信度
        self.confidence_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 1),
            nn.Sigmoid()  # 将输出压缩到 0-1 范围
        )

    def forward(self, input_data: torch.Tensor):
        """
        CIFE 前向传播 - 支持图像块和特征向量两种输入

        输入:
        - image_crops (B, 3, H, W) - B 是 proposals 的数量 (原始用法)
        - features (B, D) - B 是检测数量，D 是特征维度 (CIFT中的用法)

        输出: (clean_features, confidence_scores)
            - clean_features (B, D): 提纯后的特征
            - confidence_scores (B, 1): 特征质量置信度
        """

        # 判断输入类型
        if input_data.dim() == 4:
            # 图像块输入 (B, 3, H, W)
            x = self._process_image_crops(input_data)
        elif input_data.dim() == 2:
            # 特征向量输入 (B, D) - CIFT中的用法
            x = self._process_feature_vectors(input_data)
        else:
            raise ValueError(f"不支持的输入维度: {input_data.dim()}, 期望 2D (特征) 或 4D (图像)")

        # Encoder - Mamba特征提纯
        for mamba_block in self.encoder:
            x = mamba_block(x)

        # Fusion - 获得提纯特征
        if x.dim() == 3:  # (B, L, D)
            x = rearrange(x, 'b l d -> b d l')
            clean_features = self.fusion(x).squeeze(-1)  # (B, D)
        else:  # (B, D)
            clean_features = x

        clean_features = self.norm(clean_features)

        # ================== 预测置信度 ==================
        confidence_scores = self.confidence_head(clean_features)  # 输出维度: [B, 1]

        return clean_features, confidence_scores

    def _process_image_crops(self, image_crops):
        """处理图像块输入"""
        # Tokenizer
        x = self.tokenizer(image_crops)  # (B, D, H_p, W_p)
        x = rearrange(x, 'b d h w -> b (h w) d')

        # 添加位置编码
        x = x + self.pos_embed[:, :x.shape[1], :]

        return x

    def _process_feature_vectors(self, features):
        """处理特征向量输入 - CIFT中使用"""
        # 将特征向量转换为序列格式以便Mamba处理
        # (B, D) -> (B, 1, D) 单个特征向量作为长度为1的序列
        x = features.unsqueeze(1)  # (B, 1, D)

        # 不需要位置编码，因为只有一个元素
        return x
