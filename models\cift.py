# 文件: models/cift.py
# CIFT (Causal Instance Filtering Tracker) - 正确的架构实现
# 流程: 预训练DETR -> CIFE -> CIFT

import torch
import torch.nn.functional as F
from torch import nn
from typing import List, Optional
import torchvision.ops

from util.misc import (NestedTensor, nested_tensor_from_tensor_list,
                       inverse_sigmoid)
from util import box_ops

from .cife import CIFE
from .gtm import GatedTrackMixer
from .criterion import SetCriterion
from .matcher import HungarianMatcher
from .deformable_detr.deformable_detr import MLP


class CIFT(nn.Module):
    """
    CIFT (Causal Instance Filtering Tracker)
    
    正确的架构：
    1. 基础检测器 (预训练的Deformable DETR) - 冻结参数
    2. CIFE 特征去噪器 - 处理检测器输出的特征
    3. 因果追踪头 - GTM + 固定轨迹嵌入
    """
    
    def __init__(self, base_detector, hidden_dim=256, num_track_slots=500):
        super().__init__()
        
        # ================== 1. 基础检测器 ==================
        self.base_detector = base_detector
        self.hidden_dim = hidden_dim
        self.num_track_slots = num_track_slots
        
        # 冻结基础检测器的参数
        for param in self.base_detector.parameters():
            param.requires_grad_(False)
        
        print(f"🔒 基础检测器参数已冻结")
        
        # ================== 2. CIFE 特征去噪器 ==================
        self.cife = CIFE(
            d_model=hidden_dim,
            n_layers=4,
            patch_size=16,  # 这个参数在特征去噪中不会用到
            state_dim=16
        )
        
        # ================== 3. GAM (Global Association Matrix) 系统 ==================
        self.track_embeds = nn.Parameter(torch.randn(num_track_slots, hidden_dim))
        self.temporal_updater = nn.GRUCell(hidden_dim, hidden_dim)

        # ================== 4. 时序特征聚合系统 ==================
        # 定义2D自适应平均池化层，将(C,H,W)聚合为(C,1,1)
        self.temporal_feature_pool = nn.AdaptiveAvgPool2d(1)

        # Mamba时序处理器 (简化版本，后续可扩展)
        from .mamba_block import MambaBlock
        self.mamba_processor = MambaBlock(d_model=hidden_dim, d_state=16)
        
        # ================== 4. GTM 门控轨迹混合器 ==================
        self.gtm = GatedTrackMixer(d_model=hidden_dim)
        
        # ================== 5. 预测头 ==================
        self.bbox_embed = MLP(hidden_dim, hidden_dim, 4, 3)
        self.class_embed = nn.Linear(hidden_dim, num_track_slots + 1)
        
        # ================== 6. 匹配器和损失函数 ==================
        cost_class = 2.0
        cost_bbox = 5.0
        cost_giou = 2.0
        
        self.matcher = HungarianMatcher(
            cost_class=cost_class, 
            cost_bbox=cost_bbox, 
            cost_giou=cost_giou
        )
        
        weight_dict = {
            'loss_ce': cost_class, 
            'loss_bbox': cost_bbox, 
            'loss_giou': cost_giou
        }
        
        losses = ['labels', 'boxes']
        
        self.criterion = SetCriterion(
            num_classes=num_track_slots,
            matcher=self.matcher,
            weight_dict=weight_dict,
            losses=losses
        )
        
        # ================== 7. 时序状态管理 ==================
        self.temporal_states = None
        self.is_initialized = False
        
        print(f"✅ CIFT 初始化完成:")
        print(f"   - 轨迹槽位: {num_track_slots}")
        print(f"   - 隐藏维度: {hidden_dim}")
        print(f"   - 基础检测器: {type(base_detector).__name__}")
    
    def reset(self):
        """重置模型的时序状态，在每个新视频序列开始时调用"""
        self.temporal_states = None
        self.is_initialized = False

    def forward(self, data_dict):
        """
        CIFT 前向传播 - "智能装配工厂"架构

        黄金标准流程:
        阶段1: 统一原材料加工 (Backbone特征提取，全程仅此一次)
        阶段2: 兵分两路并行装配
          - 流水线A: 检测监督线 (训练检测头)
          - 流水线B: 时序跟踪线 (学习物体身份)
        阶段3: 汇总报告，指导生产 (损失计算和反向传播)
        """
        # ================== 阶段0: 数据准备 ==================
        if isinstance(data_dict, dict):
            imgs_sequence = data_dict['imgs']  # Shape: (B, T, C, H, W)
            targets = data_dict.get('gt_instances', None)
        else:
            imgs_sequence = data_dict
            targets = None

        # 确保输入格式正确
        if imgs_sequence.dim() == 4:  # (T, C, H, W) -> (1, T, C, H, W)
            imgs_sequence = imgs_sequence.unsqueeze(0)

        B, T, C, H, W = imgs_sequence.shape

        # 准备所有帧的输入
        all_frames_tensor = imgs_sequence.flatten(0, 1)  # (B*T, C, H, W)
        all_frames_nested = nested_tensor_from_tensor_list([all_frames_tensor[i] for i in range(all_frames_tensor.shape[0])])

        # ================== 阶段1: 统一原材料加工 (Backbone特征提取) ==================
        with torch.no_grad():  # 冻结检测器参数
            all_features, all_pos = self.base_detector.backbone(all_frames_nested)

        # ================== 阶段2: 准备两路流水线的输入 ==================
        # 使用view + slice方法获取最后一帧特征 (推荐方法)
        last_frame_features_list = []
        last_frame_pos_list = []

        for feat_lvl, pos_lvl in zip(all_features, all_pos):
            src, mask = feat_lvl.decompose()

            # 将 (B*T, C, H, W) 变回 (B, T, C, H, W)
            src_temporal = src.view(B, T, *src.shape[1:])
            mask_temporal = mask.view(B, T, *mask.shape[1:]) if mask is not None else None
            pos_temporal = pos_lvl.view(B, T, *pos_lvl.shape[1:])

            # 直接切片最后一帧 [:, -1]
            last_frame_src = src_temporal[:, -1]
            last_frame_mask = mask_temporal[:, -1] if mask_temporal is not None else None
            last_frame_pos = pos_temporal[:, -1]

            # 创建默认mask如果需要
            if last_frame_mask is None:
                last_frame_mask = torch.zeros(last_frame_src.shape[0], last_frame_src.shape[2], last_frame_src.shape[3],
                                            dtype=torch.bool, device=last_frame_src.device)

            last_frame_features_list.append(NestedTensor(last_frame_src, last_frame_mask))
            last_frame_pos_list.append(last_frame_pos)

        # 准备最后一帧的NestedTensor用于检测头
        last_frame_imgs_tensor = imgs_sequence[:, -1]  # (B, C, H, W)
        last_frame_nested = nested_tensor_from_tensor_list([last_frame_imgs_tensor[i] for i in range(last_frame_imgs_tensor.shape[0])])

        # 准备最后一帧的targets
        if targets is not None and len(targets) > 0:
            targets_for_last_frame = targets[-1] if isinstance(targets, list) else [targets[-1]]
        else:
            targets_for_last_frame = None

        # 初始化时序状态
        if self.temporal_states is None or not self.is_initialized:
            device = imgs_sequence.device
            self.temporal_states = torch.zeros_like(self.track_embeds).to(device)
            self.is_initialized = True
        
        # ================== 阶段3: 启动两条流水线 ==================

        # === 流水线A: 检测监督线 (目标：训练检测头) ===
        with torch.no_grad():  # 如果整个检测器都冻结
            det_outputs = self.base_detector.forward_from_features(
                last_frame_features_list,
                last_frame_pos_list,
                last_frame_nested
            )

        # === 流水线B: 时序跟踪线 (目标：学习和维持物体身份) ===

        # 1. 获取图纸 (RoIs)
        pred_boxes = det_outputs['pred_boxes']  # (batch, num_queries, 4)
        if pred_boxes.dim() == 3 and pred_boxes.shape[0] == 1:
            pred_boxes = pred_boxes.squeeze(0)  # (num_queries, 4)

        # 2. 精密数控加工 (RoIAlign)
        # 选择合适的特征层级进行RoIAlign (使用第2或第3层)
        if len(last_frame_features_list) >= 3:
            feature_map_for_roi = last_frame_features_list[2].tensors  # 第3层特征
        else:
            feature_map_for_roi = last_frame_features_list[-1].tensors  # 最后一层

        # 使用辅助函数进行RoIAlign (更健壮)
        roi_features = self._extract_roi_features(
            feature_map_for_roi, pred_boxes, imgs_sequence.shape[-2:]
        )

        # 3. 特征精炼 (CIFE)
        if roi_features.shape[0] > 0:
            refined_det_embeds, confidence_scores = self.cife(roi_features)  # CIFE接收特征图输入
        else:
            refined_det_embeds = torch.empty(0, self.hidden_dim, device=feature_map_for_roi.device)
            confidence_scores = torch.empty(0, 1, device=feature_map_for_roi.device)

        # 4. 时序融合 (GTM) & 关联
        # 准备上一帧的轨迹信息
        predicted_embeds = self.temporal_updater(
            self.track_embeds.to(feature_map_for_roi.device),
            self.temporal_states.to(feature_map_for_roi.device)
        )

        # 计算关联代价矩阵
        if refined_det_embeds.shape[0] > 0:
            refined_det_embeds_norm = F.normalize(refined_det_embeds, p=2, dim=1)
            predicted_embeds_norm = F.normalize(predicted_embeds, p=2, dim=1)
            cost_matrix = torch.matmul(refined_det_embeds_norm, predicted_embeds_norm.t())
        else:
            cost_matrix = torch.empty(0, self.num_track_slots, device=feature_map_for_roi.device)

        # 5. 匈牙利匹配
        num_detections = refined_det_embeds.shape[0]
        if num_detections > 0:
            # 为匹配器准备输出格式
            dummy_logits = torch.zeros(1, num_detections, 1, device=feature_map_for_roi.device)

            out_for_matcher = {
                'pred_logits': dummy_logits,
                'pred_boxes': pred_boxes.unsqueeze(0)  # (1, num_detections, 4)
            }

            # 执行匹配
            if targets_for_last_frame is not None and self.training:
                try:
                    indices = self.matcher(out_for_matcher, targets_for_last_frame)
                    if indices and len(indices) > 0:
                        matched_det_idx, matched_track_idx = indices[0]
                    else:
                        matched_det_idx = torch.tensor([], dtype=torch.long, device=feature_map_for_roi.device)
                        matched_track_idx = torch.tensor([], dtype=torch.long, device=feature_map_for_roi.device)
                except Exception as e:
                    print(f"⚠️ 匹配器错误: {e}")
                    # 使用贪心匹配作为回退
                    if cost_matrix.shape[0] > 0:
                        _, matched_track_idx = cost_matrix.max(dim=1)
                        matched_det_idx = torch.arange(num_detections, device=feature_map_for_roi.device)
                    else:
                        matched_det_idx = torch.tensor([], dtype=torch.long, device=feature_map_for_roi.device)
                        matched_track_idx = torch.tensor([], dtype=torch.long, device=feature_map_for_roi.device)
            else:
                # 推理时使用贪心匹配
                if cost_matrix.shape[0] > 0:
                    _, matched_track_idx = cost_matrix.max(dim=1)
                    matched_det_idx = torch.arange(num_detections, device=feature_map_for_roi.device)
                else:
                    matched_det_idx = torch.tensor([], dtype=torch.long, device=feature_map_for_roi.device)
                    matched_track_idx = torch.tensor([], dtype=torch.long, device=feature_map_for_roi.device)
        else:
            matched_det_idx = torch.tensor([], dtype=torch.long, device=feature_map_for_roi.device)
            matched_track_idx = torch.tensor([], dtype=torch.long, device=feature_map_for_roi.device)

        # 6. GTM: 门控轨迹混合
        # 准备置信度信号
        track_confidence = torch.zeros(
            self.num_track_slots, 1,
            device=predicted_embeds.device,
            dtype=predicted_embeds.dtype
        )

        if len(matched_det_idx) > 0:
            track_confidence[matched_track_idx] = confidence_scores[matched_det_idx]

        # 执行门控混合
        mixed_track_embeds = self.gtm(predicted_embeds, track_confidence)

        # 7. 更新轨迹状态
        current_track_embeds = predicted_embeds.clone()
        if len(matched_det_idx) > 0:
            # 确保数据类型匹配
            refined_det_embeds_matched = refined_det_embeds[matched_det_idx].to(current_track_embeds.dtype)
            current_track_embeds[matched_track_idx] = refined_det_embeds_matched

        # 更新时序状态（推理时）
        if not self.training:
            self.track_embeds.data = mixed_track_embeds.detach()
            self.temporal_states = current_track_embeds.detach()

        # ================== 阶段4: 计算总损失并返回 ==================
        if self.training and targets_for_last_frame is not None:
            # 计算检测损失 (来自流水线A)
            loss_dict = self.criterion(det_outputs, targets_for_last_frame)

            # 这里可以添加跟踪/关联损失 (来自流水线B)
            # 例如：ID分类损失、对比损失等
            # loss_track = self._compute_tracking_loss(refined_det_embeds, matched_det_idx, matched_track_idx)
            # loss_dict.update(loss_track)

            # 加权求和
            weight_dict = self.criterion.weight_dict
            total_loss = sum(loss_dict[k] * weight_dict[k] for k in loss_dict.keys() if k in weight_dict)

            # ================== 显存管理 ==================
            # 删除中间变量，防止显存泄漏
            del refined_det_embeds, confidence_scores, predicted_embeds
            del current_track_embeds, mixed_track_embeds, cost_matrix
            if 'matched_det_idx' in locals():
                del matched_det_idx, matched_track_idx

            return total_loss, loss_dict
        else:
            # 推理逻辑 - 返回最终的追踪结果
            output_logits = self.class_embed(mixed_track_embeds)
            output_boxes = self.bbox_embed(mixed_track_embeds).sigmoid()

            final_outputs = {
                'pred_logits': output_logits.unsqueeze(0),
                'pred_boxes': output_boxes.unsqueeze(0),
            }

            return final_outputs
    
    def _extract_roi_features(self, feature_map, boxes, image_size, output_size=(14, 14)):
        """
        使用RoIAlign从特征图中提取RoI特征

        Args:
            feature_map: backbone特征图 (1, C, H, W)
            boxes: 边界框 (N, 4) 格式为 (cx, cy, w, h) 归一化坐标
            image_size: 原始图像尺寸 (H, W)
            output_size: RoI输出尺寸

        Returns:
            cropped_features: 裁剪的特征 (N, C, output_size[0], output_size[1])
        """
        if boxes.shape[0] == 0:
            return torch.empty(0, feature_map.shape[1], *output_size,
                             device=feature_map.device, dtype=feature_map.dtype)

        # 转换边界框格式：从 (cx, cy, w, h) 到 (x1, y1, x2, y2)
        boxes_xyxy = box_ops.box_cxcywh_to_xyxy(boxes)

        # 将归一化坐标转换为特征图坐标
        img_h, img_w = image_size
        feat_h, feat_w = feature_map.shape[-2:]

        # 计算特征图相对于原图的缩放比例
        scale_h = feat_h / img_h
        scale_w = feat_w / img_w

        # 将边界框坐标转换到特征图尺度
        boxes_xyxy[:, [0, 2]] *= img_w * scale_w  # x坐标
        boxes_xyxy[:, [1, 3]] *= img_h * scale_h  # y坐标

        # 为RoIAlign准备输入：需要添加batch索引
        batch_indices = torch.zeros(boxes.shape[0], device=boxes.device, dtype=torch.float32)
        rois = torch.cat([batch_indices.unsqueeze(1), boxes_xyxy], dim=1)

        # 执行RoIAlign
        cropped_features = torchvision.ops.roi_align(
            feature_map,
            rois,
            output_size=output_size,
            spatial_scale=1.0,  # 已经在上面处理了缩放
            sampling_ratio=2
        )

        return cropped_features




# 删除了复杂的DeformableDETR类，使用简化的SimpleDetector


def build_cift(args):
    """
    构建 CIFT (Causal Instance Filtering Tracker) 模型

    正确的构建流程 (模仿 MOTIP):
    1. 从 deformable_detr 包中导入顶层构建函数 build
    2. 调用 build 函数来创建完整的、预训练的基础检测器
    3. 将创建好的 base_detector 传入 CIFT 追踪头的构造函数
    """
    # ================== 1. 导入顶层构建函数 ==================
    from .deformable_detr.deformable_detr import build as build_deformable_detr

    # 设置设备
    device = torch.device(args.device)

    # ================== 2. 构建并加载基础检测器 ==================
    print("🔧 构建基础检测器 (Deformable DETR)...")

    # 临时设置检测器需要的参数
    original_num_classes = getattr(args, 'num_classes', None)
    args.num_classes = 1  # 检测器只需要检测目标

    # 调用这个"总开关"函数，它会处理好所有内部的构建，包括backbone和transformer
    base_detector, _, _ = build_deformable_detr(args)

    # 恢复原始参数
    if original_num_classes is not None:
        args.num_classes = original_num_classes

    # 加载预训练权重
    if hasattr(args, 'pretrained') and args.pretrained:
        print(f"📥 加载预训练权重: {args.pretrained}")
        try:
            checkpoint = torch.load(args.pretrained, map_location='cpu')
            # MOTIP 的权重通常保存在 'model' 键下
            if 'model' in checkpoint:
                base_detector.load_state_dict(checkpoint['model'], strict=False)
            else:
                base_detector.load_state_dict(checkpoint, strict=False)
            print("✅ 预训练权重加载成功")
        except Exception as e:
            print(f"⚠️ 预训练权重加载失败: {e}")
            print("继续使用随机初始化的检测器")

    # ================== 3. 构建CIFT追踪头 ==================
    print("🔧 构建CIFT追踪头...")

    # 将完整的、功能完备的 base_detector 实例传递给 CIFT
    model = CIFT(
        base_detector=base_detector,
        hidden_dim=getattr(args, 'hidden_dim', 256),
        num_track_slots=getattr(args, 'num_track_slots', 500)  # 恢复到500
    )

    model.to(device)

    # CIFT模型内部已经包含了criterion，所以这里返回None
    criterion = None
    postprocessors = {}

    print(f"✅ CIFT 模型构建完成:")
    print(f"   - 基础检测器: {type(base_detector).__name__}")
    print(f"   - 轨迹槽位: {model.num_track_slots}")
    print(f"   - 隐藏维度: {model.hidden_dim}")
    print(f"   - 设备: {device}")

    return model, criterion, postprocessors


