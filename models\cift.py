# 文件: models/cift.py
# CIFT (Causal Instance Filtering Tracker) - 正确的架构实现
# 流程: 预训练DETR -> CIFE -> CIFT

import torch
import torch.nn.functional as F
from torch import nn
from typing import List, Optional
import torchvision.ops

from util.misc import (NestedTensor, nested_tensor_from_tensor_list,
                       inverse_sigmoid)
from util import box_ops

from .cife import CIFE
from .gtm import GatedTrackMixer
from .criterion import SetCriterion
from .matcher import HungarianMatcher
from .deformable_detr.deformable_detr import MLP


class CIFT(nn.Module):
    """
    CIFT (Causal Instance Filtering Tracker)
    
    正确的架构：
    1. 基础检测器 (预训练的Deformable DETR) - 冻结参数
    2. CIFE 特征去噪器 - 处理检测器输出的特征
    3. 因果追踪头 - GTM + 固定轨迹嵌入
    """
    
    def __init__(self, base_detector, hidden_dim=256, num_track_slots=500):
        super().__init__()
        
        # ================== 1. 基础检测器 ==================
        self.base_detector = base_detector
        self.hidden_dim = hidden_dim
        self.num_track_slots = num_track_slots
        
        # 冻结基础检测器的参数
        for param in self.base_detector.parameters():
            param.requires_grad_(False)
        
        print(f"🔒 基础检测器参数已冻结")
        
        # ================== 2. CIFE 特征去噪器 ==================
        self.cife = CIFE(
            d_model=hidden_dim,
            n_layers=4,
            patch_size=16,  # 这个参数在特征去噪中不会用到
            state_dim=16
        )
        
        # ================== 3. 固定轨迹嵌入系统 ==================
        self.track_embeds = nn.Parameter(torch.randn(num_track_slots, hidden_dim))
        self.temporal_updater = nn.GRUCell(hidden_dim, hidden_dim)
        
        # ================== 4. GTM 门控轨迹混合器 ==================
        self.gtm = GatedTrackMixer(d_model=hidden_dim)
        
        # ================== 5. 预测头 ==================
        self.bbox_embed = MLP(hidden_dim, hidden_dim, 4, 3)
        self.class_embed = nn.Linear(hidden_dim, num_track_slots + 1)
        
        # ================== 6. 匹配器和损失函数 ==================
        cost_class = 2.0
        cost_bbox = 5.0
        cost_giou = 2.0
        
        self.matcher = HungarianMatcher(
            cost_class=cost_class, 
            cost_bbox=cost_bbox, 
            cost_giou=cost_giou
        )
        
        weight_dict = {
            'loss_ce': cost_class, 
            'loss_bbox': cost_bbox, 
            'loss_giou': cost_giou
        }
        
        losses = ['labels', 'boxes']
        
        self.criterion = SetCriterion(
            num_classes=num_track_slots,
            matcher=self.matcher,
            weight_dict=weight_dict,
            losses=losses
        )
        
        # ================== 7. 时序状态管理 ==================
        self.temporal_states = None
        self.is_initialized = False
        
        print(f"✅ CIFT 初始化完成:")
        print(f"   - 轨迹槽位: {num_track_slots}")
        print(f"   - 隐藏维度: {hidden_dim}")
        print(f"   - 基础检测器: {type(base_detector).__name__}")
    
    def forward(self, samples, targets=None):
        """
        CIFT 前向传播
        
        正确的流程:
        1. 基础检测器提取物体特征
        2. CIFE 对特征进行去噪
        3. CAM 计算关联代价矩阵
        4. 匈牙利匹配进行分配
        5. GTM 进行轨迹混合
        6. 输出最终预测
        """
        # 0. 数据预处理
        if isinstance(samples, (list, torch.Tensor)):
            samples = nested_tensor_from_tensor_list(samples)
        
        # 初始化时序状态
        if self.temporal_states is None or not self.is_initialized:
            device = samples.tensors.device
            self.temporal_states = torch.zeros_like(self.track_embeds).to(device)
            self.is_initialized = True
        
        # ================== 1. 基础检测器提取特征 ==================
        with torch.no_grad():  # 确保检测器参数不更新
            det_outputs = self.base_detector(samples)

        # 获取检测器的输出特征和backbone特征图
        # Deformable DETR返回包含 'outputs' 键的字典，这是最后一层decoder的输出
        if 'outputs' in det_outputs:
            hs = det_outputs['outputs']  # (batch_size, num_queries, hidden_dim)
            if hs.dim() == 3 and hs.shape[0] == 1:
                hs = hs.squeeze(0)  # (num_queries, hidden_dim)
        else:
            # 如果检测器输出格式不同，需要适配
            raise ValueError(f"基础检测器输出格式不正确，缺少 'outputs' 键。可用键: {list(det_outputs.keys())}")

        # 获取backbone特征图用于RoIAlign
        backbone_features = None
        if hasattr(self.base_detector, 'backbone'):
            # 重新提取backbone特征（因为我们需要中间层特征）
            with torch.no_grad():
                features, _ = self.base_detector.backbone(samples)
                # 使用较高分辨率的特征图（通常是第3或第4层）
                if len(features) >= 3:
                    backbone_features = features[2].tensors  # 使用第3层特征
                else:
                    backbone_features = features[-1].tensors  # 使用最后一层

        # 获取参考点（边界框预测）
        # Deformable DETR直接返回pred_boxes
        if 'pred_boxes' in det_outputs:
            reference_points = det_outputs['pred_boxes']  # (batch_size, num_queries, 4)
            if reference_points.dim() == 3 and reference_points.shape[0] == 1:
                reference_points = reference_points.squeeze(0)  # (num_queries, 4)
        else:
            # 如果没有预测框，创建默认值
            reference_points = torch.zeros(hs.shape[0], 4, device=hs.device)

        num_detections = hs.shape[0]
        
        # ================== 2. CIFE 特征去噪 ==================
        if num_detections > 0 and backbone_features is not None:
            # 使用RoIAlign从backbone特征图中提取RoI特征
            cropped_features = self._extract_roi_features(
                backbone_features, reference_points, samples.tensors.shape[-2:]
            )

            # 使用CIFE处理裁剪的特征（现在CIFE应该支持特征图输入）
            if cropped_features.shape[0] > 0:
                clean_hs, confidence_scores = self.cife(cropped_features)
                # 将CIFE输出与原始检测特征融合
                clean_hs = 0.7 * hs + 0.3 * clean_hs  # 加权融合
            else:
                clean_hs = hs
                confidence_scores = torch.ones(num_detections, 1, device=hs.device)
        else:
            # 如果没有backbone特征，直接使用检测特征
            clean_hs, confidence_scores = self._cife_feature_processing(hs)
        
        # ================== 3. CAM: 关联预测与观测 ==================
        # 时序预测：用上一帧状态预测当前轨迹嵌入
        predicted_embeds = self.temporal_updater(
            self.track_embeds.to(hs.device), 
            self.temporal_states.to(hs.device)
        )
        
        # 计算关联代价矩阵
        if num_detections > 0:
            clean_hs_norm = F.normalize(clean_hs, p=2, dim=1)
            predicted_embeds_norm = F.normalize(predicted_embeds, p=2, dim=1)
            cost_matrix = torch.matmul(clean_hs_norm, predicted_embeds_norm.t())
        else:
            cost_matrix = torch.empty(0, self.num_track_slots, device=hs.device)
        
        # ================== 4. 匈牙利匹配 ==================
        # 准备匹配器输入
        if num_detections > 0:
            out_for_matcher = {
                'pred_logits': cost_matrix.unsqueeze(0),  # (1, num_detections, num_track_slots)
                'pred_boxes': reference_points.unsqueeze(0)  # (1, num_detections, 4)
            }
            
            # 执行匹配
            if targets is not None and self.training:
                indices = self.matcher(out_for_matcher, targets)
                if indices and len(indices) > 0:
                    matched_det_idx, matched_track_idx = indices[0]
                else:
                    matched_det_idx = torch.tensor([], dtype=torch.long, device=hs.device)
                    matched_track_idx = torch.tensor([], dtype=torch.long, device=hs.device)
            else:
                # 推理时使用贪心匹配
                _, matched_track_idx = cost_matrix.max(dim=1)
                matched_det_idx = torch.arange(num_detections, device=hs.device)
        else:
            matched_det_idx = torch.tensor([], dtype=torch.long, device=hs.device)
            matched_track_idx = torch.tensor([], dtype=torch.long, device=hs.device)
        
        # ================== 5. GTM: 门控轨迹混合 ==================
        # 准备置信度信号
        track_confidence = torch.zeros(
            self.num_track_slots, 1, 
            device=predicted_embeds.device, 
            dtype=predicted_embeds.dtype
        )
        
        if len(matched_det_idx) > 0:
            track_confidence[matched_track_idx] = confidence_scores[matched_det_idx]
        
        # 执行门控混合
        mixed_track_embeds = self.gtm(predicted_embeds, track_confidence)
        
        # ================== 6. 更新轨迹状态 ==================
        current_track_embeds = predicted_embeds.clone()
        if len(matched_det_idx) > 0:
            current_track_embeds[matched_track_idx] = clean_hs[matched_det_idx]
        
        # 更新时序状态（推理时）
        if not self.training:
            self.track_embeds.data = mixed_track_embeds.detach()
            self.temporal_states = current_track_embeds.detach()
        
        # ================== 7. 最终预测 ==================
        output_logits = self.class_embed(mixed_track_embeds)
        output_boxes = self.bbox_embed(mixed_track_embeds).sigmoid()
        
        outputs = {
            'pred_logits': output_logits.unsqueeze(0),
            'pred_boxes': output_boxes.unsqueeze(0),
        }
        
        # ================== 8. 损失计算 ==================
        if targets is not None and self.training:
            loss_dict = self.criterion(outputs, targets)
            return loss_dict
        
        return outputs
    
    def _extract_roi_features(self, feature_map, boxes, image_size, output_size=(14, 14)):
        """
        使用RoIAlign从特征图中提取RoI特征

        Args:
            feature_map: backbone特征图 (1, C, H, W)
            boxes: 边界框 (N, 4) 格式为 (cx, cy, w, h) 归一化坐标
            image_size: 原始图像尺寸 (H, W)
            output_size: RoI输出尺寸

        Returns:
            cropped_features: 裁剪的特征 (N, C, output_size[0], output_size[1])
        """
        if boxes.shape[0] == 0:
            return torch.empty(0, feature_map.shape[1], *output_size,
                             device=feature_map.device, dtype=feature_map.dtype)

        # 转换边界框格式：从 (cx, cy, w, h) 到 (x1, y1, x2, y2)
        boxes_xyxy = box_ops.box_cxcywh_to_xyxy(boxes)

        # 将归一化坐标转换为特征图坐标
        img_h, img_w = image_size
        feat_h, feat_w = feature_map.shape[-2:]

        # 计算特征图相对于原图的缩放比例
        scale_h = feat_h / img_h
        scale_w = feat_w / img_w

        # 将边界框坐标转换到特征图尺度
        boxes_xyxy[:, [0, 2]] *= img_w * scale_w  # x坐标
        boxes_xyxy[:, [1, 3]] *= img_h * scale_h  # y坐标

        # 为RoIAlign准备输入：需要添加batch索引
        batch_indices = torch.zeros(boxes.shape[0], device=boxes.device, dtype=torch.float32)
        rois = torch.cat([batch_indices.unsqueeze(1), boxes_xyxy], dim=1)

        # 执行RoIAlign
        cropped_features = torchvision.ops.roi_align(
            feature_map,
            rois,
            output_size=output_size,
            spatial_scale=1.0,  # 已经在上面处理了缩放
            sampling_ratio=2
        )

        return cropped_features

    def _cife_feature_processing(self, features):
        """
        使用CIFE处理检测特征（回退方法）

        Args:
            features: 检测器输出的特征 (num_detections, hidden_dim)

        Returns:
            clean_features: 去噪后的特征
            confidence_scores: 置信度分数
        """
        # 直接使用CIFE的新接口，它现在支持特征向量输入
        clean_features, confidence_scores = self.cife(features)

        return clean_features, confidence_scores


# 删除了复杂的DeformableDETR类，使用简化的SimpleDetector


def build_cift(args):
    """
    构建 CIFT (Causal Instance Filtering Tracker) 模型

    正确的构建流程 (模仿 MOTIP):
    1. 从 deformable_detr 包中导入顶层构建函数 build
    2. 调用 build 函数来创建完整的、预训练的基础检测器
    3. 将创建好的 base_detector 传入 CIFT 追踪头的构造函数
    """
    # ================== 1. 导入顶层构建函数 ==================
    from .deformable_detr.deformable_detr import build as build_deformable_detr

    # 设置设备
    device = torch.device(args.device)

    # ================== 2. 构建并加载基础检测器 ==================
    print("🔧 构建基础检测器 (Deformable DETR)...")

    # 临时设置检测器需要的参数
    original_num_classes = getattr(args, 'num_classes', None)
    args.num_classes = 1  # 检测器只需要检测目标

    # 调用这个"总开关"函数，它会处理好所有内部的构建，包括backbone和transformer
    base_detector, _, _ = build_deformable_detr(args)

    # 恢复原始参数
    if original_num_classes is not None:
        args.num_classes = original_num_classes

    # 加载预训练权重
    if hasattr(args, 'pretrained') and args.pretrained:
        print(f"📥 加载预训练权重: {args.pretrained}")
        try:
            checkpoint = torch.load(args.pretrained, map_location='cpu')
            # MOTIP 的权重通常保存在 'model' 键下
            if 'model' in checkpoint:
                base_detector.load_state_dict(checkpoint['model'], strict=False)
            else:
                base_detector.load_state_dict(checkpoint, strict=False)
            print("✅ 预训练权重加载成功")
        except Exception as e:
            print(f"⚠️ 预训练权重加载失败: {e}")
            print("继续使用随机初始化的检测器")

    # ================== 3. 构建CIFT追踪头 ==================
    print("🔧 构建CIFT追踪头...")

    # 将完整的、功能完备的 base_detector 实例传递给 CIFT
    model = CIFT(
        base_detector=base_detector,
        hidden_dim=getattr(args, 'hidden_dim', 256),
        num_track_slots=getattr(args, 'num_track_slots', 500)
    )

    model.to(device)

    # CIFT模型内部已经包含了criterion，所以这里返回None
    criterion = None
    postprocessors = {}

    print(f"✅ CIFT 模型构建完成:")
    print(f"   - 基础检测器: {type(base_detector).__name__}")
    print(f"   - 轨迹槽位: {model.num_track_slots}")
    print(f"   - 隐藏维度: {model.hidden_dim}")
    print(f"   - 设备: {device}")

    return model, criterion, postprocessors


