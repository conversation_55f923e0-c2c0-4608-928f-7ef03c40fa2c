# ------------------------------------------------------------------------
# Deformable DETR
# Copyright (c) 2020 SenseTime. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 [see LICENSE for details]
# ------------------------------------------------------------------------
# Modified from DETR (https://github.com/facebookresearch/detr)
# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
# ------------------------------------------------------------------------

# from .motr import build as build_motr  # 注释掉，专注于CIFT架构

# 导入 CIFT (Causal Instance Filtering Tracker) - 正确的架构
try:
    from .cift import build_cift
    CIFT_AVAILABLE = True
    print("✅ CIFT (Causal Instance Filtering Tracker) 可用")
except ImportError as e:
    CIFT_AVAILABLE = False
    print(f"⚠️  CIFT 不可用: {e}")

# CIPT已废弃，CIFT是正确的架构


def build_model(args):
    arch_catalog = {}

    # 添加 CIFT 架构 (唯一支持的架构)
    if CIFT_AVAILABLE:
        arch_catalog['cift'] = build_cift

    assert args.meta_arch in arch_catalog, f'invalid arch: {args.meta_arch}. Available: {list(arch_catalog.keys())}'
    build_func = arch_catalog[args.meta_arch]
    return build_func(args)

