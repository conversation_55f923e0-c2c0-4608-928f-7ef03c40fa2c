# ------------------------------------------------------------------------
# Deformable DETR
# Copyright (c) 2020 SenseTime. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 [see LICENSE for details]
# ------------------------------------------------------------------------
# Modified from DETR (https://github.com/facebookresearch/detr)
# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
# ------------------------------------------------------------------------

from .motr import build as build_motr

# 导入 CIPT (Causal Instance-Prompt Tracker) 版本
try:
    from .motr_mamba_ssm import build_cipt
    CIPT_AVAILABLE = True
    print("✅ CIPT (Causal Instance-Prompt Tracker) 可用")
except ImportError:
    CIPT_AVAILABLE = False
    print("⚠️  CIPT 不可用，使用原始实现")


def build_model(args):
    arch_catalog = {
        'motr': build_motr,
    }

    # 如果 mamba-ssm 可用，添加到架构目录
    if MAMBA_SSM_AVAILABLE:
        arch_catalog['motr_mamba_ssm'] = build_motr_mamba_ssm

    assert args.meta_arch in arch_catalog, f'invalid arch: {args.meta_arch}. Available: {list(arch_catalog.keys())}'
    build_func = arch_catalog[args.meta_arch]
    return build_func(args)

