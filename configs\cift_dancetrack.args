# CIFT (Causal Instance Filtering Tracker) DanceTrack 配置
# 正确的架构：预训练DETR -> CIFE -> CIFT
# 基于您的原始设计思想，但使用正确的实现方式

# ================== 基础设置 ==================
--output_dir
logs/cift_dancetrack

--dataset_file
e2e_dance

--mot_path
/mnt/d/Projects/Datasets

# ================== 模型架构（关键！）==================
--meta_arch
cift

--backbone
resnet50

--position_embedding
sine

--enc_layers
6

--dec_layers
6

--dim_feedforward
1024

--hidden_dim
256

--dropout
0.1

--nheads
8

--num_queries
300

# ================== CIFT 特有参数 ==================
--num_track_slots
500

--fp_ratio
0.3

--query_denoise
5

# ================== 训练参数 ==================
--lr
2e-4

--lr_backbone
2e-5

--batch_size
1

--weight_decay
1e-4

--epochs
50

--lr_drop
40

--clip_max_norm
0.1

--save_period
5

--eval_period
5

--dataset_file
e2e_dance

--coco_path
/mnt/d/Projects/Datasets/DanceTrack

# ================== 损失权重 ==================
--set_cost_class
2.0

--set_cost_bbox
5.0

--set_cost_giou
2.0

--cls_loss_coef
2.0

--bbox_loss_coef
5.0

--giou_loss_coef
2.0

# ================== 数据路径（WSL 格式）==================
--det_db
/mnt/d/Projects/Datasets/CIM_models/det_db_motrv2.json

--pretrained
/mnt/d/Projects/Datasets/CIM_models/r50_deformable_detr_coco_dancetrack.pth

# ================== 设备设置 ==================
--device
cuda

--seed
42

# ================== 模型选项 ==================
--with_box_refine

--two_stage

--num_feature_levels
4

--dec_n_points
4

--enc_n_points
4

# ================== 内存优化 ==================
--grad_frames
5

--num_workers
2

# ================== 优化选项 ==================
--use_amp

--use_compile

# ================== 数据增强 ==================
--random_drop
0.1

--fp_ratio
0.3

--merger_dropout
0.1

# ================== 评估设置 ==================
--eval

--resume
auto

# ================== 分布式训练（如果需要）==================
# --distributed

# ================== 调试选项 ==================
--debug

# ================== Wandb 日志 ==================
--track_train_loss

--track_eval_loss
