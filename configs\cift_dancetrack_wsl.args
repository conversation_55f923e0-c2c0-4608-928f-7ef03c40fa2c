# CIFT (Causal Instance Filtering Tracker) DanceTrack 配置 - WSL 版本
# 基于 Deformable DETR + CIFE + GTM 的现代追踪架构
# 推荐配置：序列长度 5，批次大小 1，适用于 24GB 显存

# ================== 基础设置 ==================
--output_dir
outputs/cift_dancetrack_wsl

--dataset_file
e2e_dance

--mot_path
/mnt/d/Projects/Datasets

# ================== 模型架构（CIFT）==================
--meta_arch
cift

--backbone
resnet50

--position_embedding
sine

--enc_layers
6

--dec_layers
6

--dim_feedforward
1024

--hidden_dim
256

--dropout
0.1

--nheads
8

--num_queries
300

# ================== CIFT 特有参数 ==================
# num_track_slots 和 track_embed_dim 将在模型内部设置

# ================== Deformable DETR 配置 ==================
--num_feature_levels
4

--dec_n_points
4

--enc_n_points
4

--with_box_refine

--focal_alpha
0.25

# ================== 训练参数 ==================
--lr
2e-4

--lr_backbone
2e-5

--lr_drop
15

--lr_scheduler
cosine

--epochs
50

--batch_size
1

--weight_decay
1e-4

--clip_max_norm
0.1

# ================== 性能优化 ==================
--use_amp

# ================== 数据采样 ==================
--sampler_lengths
5

--sampler_steps
5

--sample_mode
random_interval

--sample_interval
1

# ================== 损失权重 ==================
--cls_loss_coef
2.0

--bbox_loss_coef
5.0

--giou_loss_coef
2.0

--id_loss_coef
2.0

# ================== 数据增强 ==================
--random_drop
0

--merger_dropout
0.1

# ================== 匹配成本 ==================
--set_cost_class
2.0

--set_cost_bbox
5.0

--set_cost_giou
2.0

# ================== 保存和评估 ==================
--save_period
10

# ================== 数据路径（WSL 格式）==================
--det_db
/mnt/d/Projects/Datasets/CIM_models/det_db_motrv2.json

--pretrained
/mnt/d/Projects/Datasets/CIM_models/r50_deformable_detr_coco_dancetrack.pth

# ================== 设备设置 ==================
--device
cuda

--seed
42

# ================== 内存优化 ==================
--grad_frames
3

--num_workers
2

# ================== 恢复原始图像尺寸 ==================
--max_size
1333

--val_width
800
